import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import App from './App.jsx'
import './index.css'

// Global error handlers for better debugging
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason)

  // Don't prevent default for auth errors - they're expected
  if (event.reason?.response?.status === 401) {
    console.log('Authentication error caught globally - this is expected when not logged in')
    event.preventDefault() // Prevent the error from being logged as uncaught
  }
})

window.addEventListener('error', (event) => {
  console.error('Global error:', event.error)
})

// Suppress browser extension polyfill errors
window.addEventListener('error', (event) => {
  if (event.filename && event.filename.includes('polyfill.js')) {
    event.preventDefault()
    return false
  }
})

window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message &&
      event.reason.message.includes('Could not establish connection')) {
    event.preventDefault()
    return false
  }
})

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
