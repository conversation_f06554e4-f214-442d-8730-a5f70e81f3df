import { createContext, useContext, useState, useEffect } from 'react'
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles'
import { lightTheme, darkTheme } from '../styles/theme'
import { STORAGE_KEYS, THEME_MODES } from '../utils/constants'
import { useAuth } from './AuthContext'
import axios from '../utils/fixedAxios'

const ThemeContext = createContext()

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

export const ThemeProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth()
  const [themeMode, setThemeMode] = useState(() => {
    const saved = localStorage.getItem(STORAGE_KEYS.THEME)
    return saved || THEME_MODES.LIGHT
  })
  const [loading, setLoading] = useState(false)

  // Get the actual theme object
  const theme = themeMode === THEME_MODES.DARK ? darkTheme : lightTheme

  // Detect system theme preference
  const getSystemTheme = () => {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return THEME_MODES.DARK
    }
    return THEME_MODES.LIGHT
  }

  // Apply theme to document
  useEffect(() => {
    const actualMode = themeMode === THEME_MODES.SYSTEM ? getSystemTheme() : themeMode
    document.documentElement.setAttribute('data-theme', actualMode)
    document.body.style.backgroundColor = actualMode === THEME_MODES.DARK ? '#121212' : '#fafafa'
    document.body.style.color = actualMode === THEME_MODES.DARK ? '#ffffff' : '#212121'
  }, [themeMode])

  // Listen for system theme changes
  useEffect(() => {
    if (themeMode === THEME_MODES.SYSTEM) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const handleChange = () => {
        const systemTheme = getSystemTheme()
        document.documentElement.setAttribute('data-theme', systemTheme)
        document.body.style.backgroundColor = systemTheme === THEME_MODES.DARK ? '#121212' : '#fafafa'
        document.body.style.color = systemTheme === THEME_MODES.DARK ? '#ffffff' : '#212121'
      }

      mediaQuery.addEventListener('change', handleChange)
      return () => mediaQuery.removeEventListener('change', handleChange)
    }
  }, [themeMode])

  // Save theme preference locally
  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.THEME, themeMode)
  }, [themeMode])

  // Sync with backend when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      syncThemeWithBackend()
    }
  }, [isAuthenticated, user])

  const syncThemeWithBackend = async () => {
    try {
      setLoading(true)
      // Get user's theme preference from backend
      const response = await axios.get('/api/users/me/theme')
      if (response.data.success && response.data.data.mode) {
        const backendTheme = response.data.data.mode
        if (backendTheme !== themeMode) {
          setThemeMode(backendTheme)
        }
      }
    } catch (error) {
      console.warn('Failed to sync theme with backend:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateThemePreference = async (newMode) => {
    try {
      setLoading(true)
      setThemeMode(newMode)

      // Update backend if user is authenticated
      if (isAuthenticated) {
        await axios.put('/api/users/me/theme', {
          mode: newMode
        })
      }
    } catch (error) {
      console.error('Failed to update theme preference:', error)
      // Revert on error
      setThemeMode(themeMode)
    } finally {
      setLoading(false)
    }
  }

  const toggleTheme = () => {
    const newMode = themeMode === THEME_MODES.DARK ? THEME_MODES.LIGHT : THEME_MODES.DARK
    updateThemePreference(newMode)
  }

  const setSystemTheme = () => {
    updateThemePreference(THEME_MODES.SYSTEM)
  }

  const value = {
    themeMode,
    theme,
    loading,
    toggleTheme,
    setTheme: updateThemePreference,
    setSystemTheme,
    isDark: themeMode === THEME_MODES.DARK || (themeMode === THEME_MODES.SYSTEM && getSystemTheme() === THEME_MODES.DARK),
    isLight: themeMode === THEME_MODES.LIGHT || (themeMode === THEME_MODES.SYSTEM && getSystemTheme() === THEME_MODES.LIGHT),
    isSystem: themeMode === THEME_MODES.SYSTEM,
    actualTheme: themeMode === THEME_MODES.SYSTEM ? getSystemTheme() : themeMode
  }

  return (
    <ThemeContext.Provider value={value}>
      <MuiThemeProvider theme={theme}>
        {children}
      </MuiThemeProvider>
    </ThemeContext.Provider>
  )
}

export default ThemeProvider
