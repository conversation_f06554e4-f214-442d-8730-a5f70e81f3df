import axios from '../utils/fixedAxios'

class PostsService {
  // Get all posts
  async getPosts(params = {}) {
    const response = await axios.get('/api/posts', { params })
    return response.data.data || response.data.posts || []
  }

  // Get posts by user
  async getUserPosts(userId, params = {}) {
    const response = await axios.get(`/api/posts/user/${userId}`, { params })
    return response.data.data || response.data.posts || []
  }

  // Get single post
  async getPost(postId) {
    const response = await axios.get(`/api/posts/${postId}`)
    return response.data.data || response.data.post
  }

  // Create post
  async createPost(postData) {
    const formData = new FormData()

    // Handle text content (backend expects 'caption' field)
    if (postData.content) {
      formData.append('caption', postData.content)
    }

    // Handle mood
    if (postData.mood) {
      formData.append('mood', postData.mood)
    }

    // Handle location
    if (postData.location) {
      formData.append('location', postData.location)
    }

    // Handle tags
    if (postData.tags && postData.tags.length > 0) {
      postData.tags.forEach(tag => {
        formData.append('tags[]', tag)
      })
    }

    // Handle media files - extract the actual File objects
    if (postData.media && postData.media.length > 0) {
      postData.media.forEach((mediaItem) => {
        // If it's a media object with a file property, use the file
        if (mediaItem.file) {
          formData.append('media', mediaItem.file)
        } else if (mediaItem instanceof File) {
          // If it's already a File object
          formData.append('media', mediaItem)
        }
      })
    }

    // Handle privacy settings
    if (postData.privacy) {
      formData.append('privacy', postData.privacy)
    }

    console.log('Creating post with FormData:', {
      content: postData.content,
      mediaCount: postData.media?.length || 0,
      mood: postData.mood,
      location: postData.location,
      tags: postData.tags,
      privacy: postData.privacy
    })

    const response = await axios.post('/api/posts', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    return response.data.data || response.data.post
  }

  // Update post
  async updatePost(postId, updateData) {
    const response = await axios.put(`/api/posts/${postId}`, updateData)
    return response.data.data || response.data.post
  }

  // Delete post
  async deletePost(postId) {
    const response = await axios.delete(`/api/posts/${postId}`)
    return response.data
  }

  // Like post
  async likePost(postId) {
    const response = await axios.post(`/api/likes`, {
      targetType: 'post',
      targetId: postId
    })
    return response.data
  }

  // Unlike post
  async unlikePost(postId) {
    const response = await axios.delete(`/api/likes`, {
      data: { targetType: 'post', targetId: postId }
    })
    return response.data
  }

  // Get post comments
  async getComments(postId, params = {}) {
    const response = await axios.get(`/api/comments/post/${postId}`, { params })
    return response.data.data || response.data.comments || []
  }

  // Add comment
  async addComment(postId, content) {
    const response = await axios.post('/api/comments', {
      postId,
      content
    })
    return response.data.data || response.data.comment
  }

  // Get trending posts
  async getTrendingPosts(params = {}) {
    const response = await axios.get('/api/feed/trending', { params })
    return response.data.data || response.data.posts || []
  }

  // Get following posts
  async getFollowingPosts(params = {}) {
    const response = await axios.get('/api/feed/following', { params })
    return response.data.data || response.data.posts || []
  }

  // Search posts
  async searchPosts(query, params = {}) {
    const response = await axios.get('/api/posts/search', {
      params: { q: query, ...params }
    })
    return response.data.data || response.data.posts || []
  }

  // Report post
  async reportPost(postId, reason) {
    const response = await axios.post('/api/moderation/report', {
      targetType: 'post',
      targetId: postId,
      reason
    })
    return response.data
  }

  // Save/Unsave post
  async toggleSavePost(postId) {
    const response = await axios.post('/api/posts/save', { postId })
    return response.data
  }

  // Get saved posts
  async getSavedPosts(params = {}) {
    const response = await axios.get('/api/posts/saved', { params })
    return response.data.data || response.data.posts || []
  }
}

export default new PostsService()
