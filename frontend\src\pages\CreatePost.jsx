import React, { useState, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Con<PERSON>er,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Box,
  IconButton,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Avatar,
  Divider,
  Grid,
  LinearProgress
} from '@mui/material'
import {
  PhotoCamera,
  VideoCall,
  LocationOn,
  Mood,
  Public,
  People,
  Lock,
  Close,
  Add
} from '@mui/icons-material'
import { useSnackbar } from 'notistack'
import { useAuth } from '../context/AuthContext'
import postsService from '../services/postsService'
import { ImageUpload } from '../components/ui'

const CreatePost = () => {
  const [formData, setFormData] = useState({
    caption: '',
    media: [],
    mood: '',
    location: '',
    tags: [],
    privacy: 'public'
  })
  const [loading, setLoading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [tagInput, setTagInput] = useState('')
  const navigate = useNavigate()
  const { enqueueSnackbar } = useSnackbar()
  const { user } = useAuth()
  const fileInputRef = useRef(null)

  const moods = [
    { value: 'happy', label: 'Happy', emoji: '😊' },
    { value: 'excited', label: 'Excited', emoji: '🎉' },
    { value: 'grateful', label: 'Grateful', emoji: '🙏' },
    { value: 'thoughtful', label: 'Thoughtful', emoji: '🤔' },
    { value: 'relaxed', label: 'Relaxed', emoji: '😌' },
    { value: 'motivated', label: 'Motivated', emoji: '💪' },
    { value: 'creative', label: 'Creative', emoji: '🎨' },
    { value: 'adventurous', label: 'Adventurous', emoji: '🌟' }
  ]

  const privacyOptions = [
    { value: 'public', label: 'Public', icon: Public, description: 'Anyone can see this post' },
    { value: 'friends', label: 'Friends', icon: People, description: 'Only your friends can see this' },
    { value: 'private', label: 'Only me', icon: Lock, description: 'Only you can see this post' }
  ]

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!formData.caption.trim() && formData.media.length === 0) {
      enqueueSnackbar('Please add a caption or media', { variant: 'error' })
      return
    }

    try {
      setLoading(true)
      setUploadProgress(10)

      // Prepare post data
      const postData = {
        content: formData.caption, // Backend expects 'content' not 'caption'
        media: formData.media,
        mood: formData.mood,
        location: formData.location,
        tags: formData.tags,
        privacy: formData.privacy
      }

      setUploadProgress(50)
      await postsService.createPost(postData)
      setUploadProgress(100)

      enqueueSnackbar('Post created successfully!', { variant: 'success' })
      navigate('/')
    } catch (error) {
      console.error('Error creating post:', error)
      enqueueSnackbar(error.response?.data?.message || 'Failed to create post', { variant: 'error' })
    } finally {
      setLoading(false)
      setUploadProgress(0)
    }
  }

  const handleMediaUpload = (files) => {
    const newMedia = Array.from(files).map(file => ({
      file,
      preview: URL.createObjectURL(file),
      type: file.type.startsWith('video/') ? 'video' : 'image'
    }))
    setFormData(prev => ({
      ...prev,
      media: [...prev.media, ...newMedia]
    }))
  }

  const removeMedia = (index) => {
    setFormData(prev => ({
      ...prev,
      media: prev.media.filter((_, i) => i !== index)
    }))
  }

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }))
      setTagInput('')
    }
  }

  const removeTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  return (
    <Container maxWidth="md" sx={{ py: 3 }}>
      <Card elevation={3}>
        <CardContent sx={{ p: 3 }}>
          {/* Header */}
          <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
            <Typography variant="h4" component="h1" fontWeight="bold">
              Create Post
            </Typography>
            <IconButton onClick={() => navigate('/')} size="small">
              <Close />
            </IconButton>
          </Box>

          {/* User Info */}
          <Box display="flex" alignItems="center" gap={2} mb={3}>
            <Avatar src={user?.avatar} sx={{ width: 48, height: 48 }}>
              {user?.name?.charAt(0)?.toUpperCase()}
            </Avatar>
            <Box>
              <Typography variant="subtitle1" fontWeight={600}>
                {user?.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                @{user?.username}
              </Typography>
            </Box>
          </Box>

          <Divider sx={{ mb: 3 }} />

          <Box component="form" onSubmit={handleSubmit}>
            {/* Caption Input */}
            <TextField
              fullWidth
              multiline
              rows={4}
              placeholder="What's on your mind?"
              value={formData.caption}
              onChange={(e) => setFormData({ ...formData, caption: e.target.value })}
              variant="outlined"
              sx={{ mb: 3 }}
            />

            {/* Media Upload */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Media
              </Typography>
              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  border: '2px dashed',
                  borderColor: 'divider',
                  textAlign: 'center',
                  cursor: 'pointer',
                  '&:hover': {
                    borderColor: 'primary.main',
                    bgcolor: 'action.hover'
                  }
                }}
                onClick={() => fileInputRef.current?.click()}
              >
                <PhotoCamera sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  Click to upload photos or videos
                </Typography>
                <Typography variant="caption" color="text.disabled">
                  Supports JPG, PNG, GIF, MP4, MOV (Max 10MB each)
                </Typography>
              </Paper>

              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*,video/*"
                onChange={(e) => handleMediaUpload(e.target.files)}
                style={{ display: 'none' }}
              />

              {/* Media Preview */}
              {formData.media.length > 0 && (
                <Grid container spacing={2} sx={{ mt: 2 }}>
                  {formData.media.map((media, index) => (
                    <Grid item xs={6} sm={4} key={index}>
                      <Paper
                        elevation={2}
                        sx={{
                          position: 'relative',
                          paddingTop: '100%',
                          overflow: 'hidden',
                          borderRadius: 2
                        }}
                      >
                        {media.type === 'video' ? (
                          <video
                            src={media.preview}
                            style={{
                              position: 'absolute',
                              top: 0,
                              left: 0,
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover'
                            }}
                            controls
                          />
                        ) : (
                          <img
                            src={media.preview}
                            alt="Preview"
                            style={{
                              position: 'absolute',
                              top: 0,
                              left: 0,
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover'
                            }}
                          />
                        )}
                        <IconButton
                          size="small"
                          onClick={() => removeMedia(index)}
                          sx={{
                            position: 'absolute',
                            top: 4,
                            right: 4,
                            bgcolor: 'rgba(0,0,0,0.7)',
                            color: 'white',
                            '&:hover': {
                              bgcolor: 'rgba(0,0,0,0.9)'
                            }
                          }}
                        >
                          <Close fontSize="small" />
                        </IconButton>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              )}
            </Box>

            {/* Tags */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Tags
              </Typography>
              <Box display="flex" gap={1} mb={2} flexWrap="wrap">
                {formData.tags.map((tag, index) => (
                  <Chip
                    key={index}
                    label={`#${tag}`}
                    onDelete={() => removeTag(tag)}
                    color="primary"
                    variant="outlined"
                    size="small"
                  />
                ))}
              </Box>
              <Box display="flex" gap={1}>
                <TextField
                  size="small"
                  placeholder="Add a tag..."
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault()
                      handleAddTag()
                    }
                  }}
                  sx={{ flexGrow: 1 }}
                />
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleAddTag}
                  disabled={!tagInput.trim()}
                >
                  <Add />
                </Button>
              </Box>
            </Box>

            {/* Mood and Settings */}
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth size="small">
                  <InputLabel>Mood</InputLabel>
                  <Select
                    value={formData.mood}
                    onChange={(e) => setFormData({ ...formData, mood: e.target.value })}
                    label="Mood"
                  >
                    <MenuItem value="">
                      <em>None</em>
                    </MenuItem>
                    {moods.map((mood) => (
                      <MenuItem key={mood.value} value={mood.value}>
                        <Box display="flex" alignItems="center" gap={1}>
                          <span>{mood.emoji}</span>
                          {mood.label}
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth size="small">
                  <InputLabel>Privacy</InputLabel>
                  <Select
                    value={formData.privacy}
                    onChange={(e) => setFormData({ ...formData, privacy: e.target.value })}
                    label="Privacy"
                  >
                    {privacyOptions.map((option) => {
                      const IconComponent = option.icon
                      return (
                        <MenuItem key={option.value} value={option.value}>
                          <Box display="flex" alignItems="center" gap={1}>
                            <IconComponent fontSize="small" />
                            {option.label}
                          </Box>
                        </MenuItem>
                      )
                    })}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            {/* Location */}
            <TextField
              fullWidth
              size="small"
              placeholder="Add location..."
              value={formData.location}
              onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              InputProps={{
                startAdornment: <LocationOn sx={{ mr: 1, color: 'text.secondary' }} />
              }}
              sx={{ mb: 3 }}
            />

            {/* Progress Bar */}
            {loading && (
              <Box sx={{ mb: 2 }}>
                <LinearProgress variant="determinate" value={uploadProgress} />
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                  {uploadProgress < 50 ? 'Preparing...' : uploadProgress < 100 ? 'Uploading...' : 'Finalizing...'}
                </Typography>
              </Box>
            )}

            {/* Action Buttons */}
            <Box display="flex" gap={2} justifyContent="flex-end">
              <Button
                variant="outlined"
                onClick={() => navigate('/')}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={loading || (!formData.caption.trim() && formData.media.length === 0)}
                sx={{ minWidth: 120 }}
              >
                {loading ? 'Creating...' : 'Create Post'}
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Container>
  )
}

export default CreatePost
