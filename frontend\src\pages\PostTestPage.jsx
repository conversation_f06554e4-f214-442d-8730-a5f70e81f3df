import React, { useState } from 'react'
import {
  Container,
  <PERSON>po<PERSON>,
  Box,
  Card,
  CardContent,
  Button,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  Chip
} from '@mui/material'
import PostForm from '../components/social/PostForm'
import postsService from '../services/postsService'

const PostTestPage = () => {
  const [testResults, setTestResults] = useState([])
  const [loading, setLoading] = useState(false)
  const [showPostForm, setShowPostForm] = useState(false)

  const addTestResult = (test, success, message, data = null) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      message,
      data,
      timestamp: new Date().toISOString()
    }])
  }

  const runTests = async () => {
    setLoading(true)
    setTestResults([])

    try {
      // Test 1: Backend Health Check
      addTestResult('Backend Health', null, 'Testing backend connectivity...')
      const healthResults = await runHealthChecks()

      if (healthResults.backend.success) {
        addTestResult('Backend Health', true, 'Backend is accessible', healthResults.backend.data)
      } else {
        addTestResult('Backend Health', false, healthResults.backend.message)
        setLoading(false)
        return
      }

      // Test 2: Authentication Check
      if (healthResults.auth) {
        if (healthResults.auth.success) {
          addTestResult('Authentication', true, 'User is authenticated', healthResults.auth.data)
        } else {
          addTestResult('Authentication', false, healthResults.auth.message)
          setLoading(false)
          return
        }
      } else {
        addTestResult('Authentication', false, 'Authentication test skipped')
        setLoading(false)
        return
      }

      // Test 3: Simple Text Post
      addTestResult('Text Post', null, 'Creating simple text post...')
      try {
        const textPost = await postsService.createPost({
          content: `Test post created at ${new Date().toLocaleString()}`,
          privacy: 'public'
        })
        addTestResult('Text Post', true, 'Text post created successfully', { id: textPost._id })
      } catch (error) {
        addTestResult('Text Post', false, `Failed: ${error.message}`)
      }

      // Test 4: Post with Mood
      addTestResult('Mood Post', null, 'Creating post with mood...')
      try {
        const moodPost = await postsService.createPost({
          content: 'Testing mood functionality 😊',
          mood: 'happy',
          privacy: 'public'
        })
        addTestResult('Mood Post', true, 'Post with mood created successfully', { id: moodPost._id, mood: moodPost.mood })
      } catch (error) {
        addTestResult('Mood Post', false, `Failed: ${error.message}`)
      }

      // Test 5: Post with Tags
      addTestResult('Tagged Post', null, 'Creating post with tags...')
      try {
        const taggedPost = await postsService.createPost({
          content: 'Testing tags functionality',
          tags: ['test', 'functionality', 'social'],
          privacy: 'public'
        })
        addTestResult('Tagged Post', true, 'Post with tags created successfully', { id: taggedPost._id, tags: taggedPost.tags })
      } catch (error) {
        addTestResult('Tagged Post', false, `Failed: ${error.message}`)
      }

      // Test 6: Post with Location
      addTestResult('Location Post', null, 'Creating post with location...')
      try {
        const locationPost = await postsService.createPost({
          content: 'Testing location functionality',
          location: 'Test Location, Test City',
          privacy: 'public'
        })
        addTestResult('Location Post', true, 'Post with location created successfully', { id: locationPost._id, location: locationPost.location })
      } catch (error) {
        addTestResult('Location Post', false, `Failed: ${error.message}`)
      }

    } catch (error) {
      addTestResult('General Error', false, `Unexpected error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handlePostSubmit = async (postData) => {
    try {
      const result = await postsService.createPost(postData)
      addTestResult('Form Post', true, 'Post created via form successfully', { id: result._id })
      setShowPostForm(false)
    } catch (error) {
      addTestResult('Form Post', false, `Form post failed: ${error.message}`)
    }
  }

  const getStatusColor = (success) => {
    if (success === null) return 'warning'
    return success ? 'success' : 'error'
  }

  const getStatusText = (success) => {
    if (success === null) return 'Testing...'
    return success ? 'PASS' : 'FAIL'
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h3" gutterBottom>
        Post Creation Test Suite
      </Typography>

      <Typography variant="body1" color="text.secondary" gutterBottom>
        Comprehensive testing of post creation functionality
      </Typography>

      <Box sx={{ mb: 4, display: 'flex', gap: 2 }}>
        <Button
          variant="contained"
          onClick={runTests}
          disabled={loading}
          size="large"
        >
          {loading ? 'Running Tests...' : 'Run All Tests'}
        </Button>

        <Button
          variant="outlined"
          onClick={() => setShowPostForm(true)}
          disabled={loading}
        >
          Test Post Form
        </Button>

        <Button
          variant="outlined"
          onClick={() => setTestResults([])}
          disabled={loading}
        >
          Clear Results
        </Button>
      </Box>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              Test Results
            </Typography>

            <List>
              {testResults.map((result, index) => (
                <React.Fragment key={index}>
                  <ListItem>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={2}>
                          <Typography variant="subtitle1">
                            {result.test}
                          </Typography>
                          <Chip
                            label={getStatusText(result.success)}
                            color={getStatusColor(result.success)}
                            size="small"
                          />
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {result.message}
                          </Typography>
                          {result.data && (
                            <Typography variant="caption" component="pre" sx={{ mt: 1, display: 'block' }}>
                              {JSON.stringify(result.data, null, 2)}
                            </Typography>
                          )}
                          <Typography variant="caption" color="text.disabled">
                            {new Date(result.timestamp).toLocaleTimeString()}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < testResults.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      )}

      {/* Test Summary */}
      {testResults.length > 0 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Test Summary
            </Typography>

            <Box display="flex" gap={2} flexWrap="wrap">
              <Chip
                label={`Total: ${testResults.length}`}
                color="default"
              />
              <Chip
                label={`Passed: ${testResults.filter(r => r.success === true).length}`}
                color="success"
              />
              <Chip
                label={`Failed: ${testResults.filter(r => r.success === false).length}`}
                color="error"
              />
              <Chip
                label={`Running: ${testResults.filter(r => r.success === null).length}`}
                color="warning"
              />
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Post Form Dialog */}
      <PostForm
        open={showPostForm}
        onClose={() => setShowPostForm(false)}
        onSubmit={handlePostSubmit}
      />
    </Container>
  )
}

export default PostTestPage
