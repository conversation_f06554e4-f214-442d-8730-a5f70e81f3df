import axios from 'axios'

// Function to get dynamic configuration
const getDynamicConfig = () => {
  // Try to get from window.dynamicConfig first
  if (window.dynamicConfig) {
    return window.dynamicConfig
  }

  // Try to get from script tag
  try {
    const configScript = document.getElementById('dynamic-config')
    if (configScript) {
      const config = JSON.parse(configScript.textContent)
      window.dynamicConfig = config
      return config
    }
  } catch (error) {
    console.warn('Failed to parse dynamic config:', error)
  }

  // Fallback to default configuration
  return {
    apiBaseUrl: 'http://localhost:10001',
    socketUrl: 'http://localhost:10001'
  }
}

// Create axios instance with dynamic base URL
const createAxiosInstance = () => {
  const config = getDynamicConfig()

  console.log('Creating axios instance with config:', config)

  const instance = axios.create({
    baseURL: config.apiBaseUrl,
    timeout: 30000, // Increased timeout to 30 seconds
    withCredentials: true, // Enable cookies for authentication
    headers: {
      'Content-Type': 'application/json'
    }
  })

  // Request interceptor to add auth token
  instance.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
        console.log('Adding auth token to request:', config.url, 'Token:', token.substring(0, 20) + '...')
      } else {
        console.log('No auth token found for request:', config.url)
      }

      // Fix URL construction issues
      if (config.url && config.baseURL) {
        // Ensure baseURL doesn't end with slash and url doesn't start with slash for proper concatenation
        const baseURL = config.baseURL.replace(/\/$/, '')
        const url = config.url.startsWith('/') ? config.url : `/${config.url}`

        // Don't modify the config.url if it's already absolute
        if (!config.url.startsWith('http')) {
          config.url = url
        }
      }

      console.log('Final request config:', {
        url: config.url,
        baseURL: config.baseURL,
        method: config.method,
        hasAuth: !!config.headers.Authorization
      })

      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // Response interceptor to handle token refresh
  instance.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config

      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true

        try {
          const refreshToken = localStorage.getItem('refreshToken')
          if (refreshToken) {
            const response = await axios.post(`${getDynamicConfig().apiBaseUrl}/api/auth/refresh-token`, {
              refreshToken
            })

            const { token } = response.data
            localStorage.setItem('token', token)

            // Retry the original request with new token
            originalRequest.headers.Authorization = `Bearer ${token}`
            return instance(originalRequest)
          }
        } catch (refreshError) {
          // Refresh failed, redirect to login
          localStorage.removeItem('token')
          localStorage.removeItem('refreshToken')
          window.location.href = '/auth'
          return Promise.reject(refreshError)
        }
      }

      return Promise.reject(error)
    }
  )

  return instance
}

// Create and export the axios instance
const axiosInstance = createAxiosInstance()

export default axiosInstance
